# Test Configuration for Core System Tests
# This configuration matches the schema requirements

system:
  name: "Advanced Ollama Trading Agents Test"
  version: "1.0.0"
  environment: "testing"
  log_level: "DEBUG"
  debug: true
  metrics_enabled: true
  profiling_enabled: false

# Required database section (singular)
database:
  postgres:
    host: "localhost"
    port: 5432
    database: "trading_test"
    username: "test_user"
    password: "test_pass"
    pool_size: 5
    max_overflow: 10
    pool_timeout: 30
    pool_recycle: 3600
    ssl_mode: "disable"
  
  redis:
    host: "localhost"
    port: 6379
    db: 0
    password: null
    ssl: false
    max_connections: 10

# Required API section
api:
  host: "127.0.0.1"
  port: 8001
  workers: 1
  max_request_size: 10485760
  request_timeout: 300
  cors_enabled: true
  rate_limiting:
    enabled: false
    requests_per_minute: 1000
    burst_size: 100

# Required Ollama section
ollama:
  base_url: "http://localhost:11434"
  timeout: 300
  max_retries: 3
  retry_delay: 5
  connection_pool_size: 5
  load_balancing: false
  health_check_interval: 60

# Required agents section
agents:
  max_agents: 10
  heartbeat_interval: 30
  max_memory_usage: 536870912  # 512MB
  max_cpu_usage: 0.8
  defaults:
    model: "llama2:7b"
    temperature: 0.7
    max_tokens: 1024
    timeout: 120
  scaling:
    auto_scaling: false
    min_agents: 1
    max_agents: 5
    scale_up_threshold: 0.8
    scale_down_threshold: 0.3
    cooldown_period: 60

# Market data configuration
market_data:
  providers:
    mock:
      enabled: true
      symbols: ["AAPL", "GOOGL", "MSFT"]
      update_interval: 1
    alpha_vantage:
      api_key: "demo"
      rate_limit: 5
      timeout: 30
      enabled: false
  update_interval: 5
  symbols: ["AAPL", "GOOGL", "MSFT"]

# Brokers configuration
brokers:
  providers:
    mock:
      enabled: true
    paper_trading:
      enabled: true
  routing:
    default: "mock"
    symbols: {}

# Strategies configuration
strategies:
  max_active_strategies: 5
  default_capital_allocation: 0.2
  rebalance_frequency: "manual"
  risk_limits:
    max_drawdown: 0.2
    max_leverage: 1.0
    position_timeout: 3600

# Required risk section
risk:
  max_portfolio_risk: 0.02
  max_position_size: 0.1
  max_drawdown: 0.15
  max_sector_exposure: 0.5
  var_calculation:
    confidence_level: 0.95
    lookback_period: 30
    monte_carlo_simulations: 1000

# Required execution section
execution:
  paper_trading: true
  max_orders_per_second: 5
  order_timeout: 60
  slippage_model: "fixed"

# Required portfolio section
portfolio:
  initial_capital: 100000.0
  rebalance_threshold: 0.05
  max_positions: 10
  cash_target: 0.1

# Monitoring configuration
monitoring:
  metrics:
    collection_interval: 30
    retention_period: 86400
  alerts:
    email_enabled: false
    slack_enabled: false
    webhook_enabled: false
    console_enabled: true
  logging:
    level: "DEBUG"
    format: "text"
    rotation: "never"
    retention: 7

# Security configuration
security:
  jwt_secret: "test-secret-key-for-testing-only-32chars"
  jwt_expiry: 3600
  api_key_required: false
  encryption_enabled: false
  audit_logging: false
  ip_whitelist: []

# Performance configuration
performance:
  caching:
    enabled: true
    backend: "memory"
    default_ttl: 60
  connection_pooling:
    database: 5
    redis: 10
    http: 20
  async_processing:
    max_workers: 2
    queue_size: 100
    batch_size: 10

# Backup configuration
backup:
  enabled: false
  schedule: "0 2 * * *"
  retention: 7
  destinations:
    - type: "local"
      path: "backup/test"

# Compliance configuration
compliance:
  enabled: false
  reporting:
    daily_reports: false
    monthly_reports: false
    regulatory_reports: false
  audit_trail:
    enabled: false
    retention: 30
    encryption: false

# Notification configuration
notifications:
  email:
    enabled: false
  slack:
    enabled: false
  webhook:
    enabled: false
  console:
    enabled: true
    level: "INFO"

# Testing-specific configuration
testing:
  mock_external_apis: true
  fast_mode: true
  skip_validations: false
  generate_mock_data: true
  mock_data_size: 100
  historical_data_days: 30
